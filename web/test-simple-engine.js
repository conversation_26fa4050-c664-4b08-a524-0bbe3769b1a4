/**
 * Simple test script to verify the letter template engine core functionality
 */

const fs = require('fs');
const path = require('path');
const handlebars = require('handlebars');

console.log('🚀 Testing Letter Template Engine Core Functionality...\n');

// Test 1: Check if template files exist
console.log('📋 Test 1: Template Files Check');
const templateIds = [
  'plain-text',
  'classic-blue', 
  'professional-classic',
  'minimalist-sidebar',
  'minimalist-border-frame',
  'minimalist-accent',
  'minimalist-circular-accents'
];

const templatesDir = path.join(__dirname, 'utils/handlebars-templates/letters');
console.log('Templates directory:', templatesDir);

templateIds.forEach(templateId => {
  const templatePath = path.join(templatesDir, `${templateId}.hbs`);
  const exists = fs.existsSync(templatePath);
  console.log(`   ${templateId}: ${exists ? '✅ Found' : '❌ Missing'}`);
});
console.log();

// Test 2: Load and compile a template
console.log('🔧 Test 2: Template Compilation');
try {
  const templatePath = path.join(templatesDir, 'plain-text.hbs');
  
  if (!fs.existsSync(templatePath)) {
    throw new Error('Plain text template not found');
  }
  
  const templateSource = fs.readFileSync(templatePath, 'utf8');
  console.log(`✅ Template source loaded (${templateSource.length} characters)`);
  
  // Register helpers
  handlebars.registerHelper('unless', function(conditional, options) {
    if (!conditional) {
      return options.fn(this);
    } else {
      return options.inverse(this);
    }
  });
  
  const compiledTemplate = handlebars.compile(templateSource);
  console.log('✅ Template compiled successfully');
  
  // Test 3: Render with sample data
  console.log('\n🎨 Test 3: Template Rendering');
  
  const sampleData = {
    date: 'Jakarta, 29 Juli 2025',
    subject: 'Lamaran Kerja - Software Developer',
    recipientLines: [
      'Yth. HRD Manager',
      'PT Example Company',
      'Jakarta'
    ],
    opening: 'Dengan hormat,',
    paragraphs: [
      'Saya yang bertanda tangan di bawah ini bermaksud mengajukan permohonan untuk dapat bergabung dengan perusahaan yang Bapak/Ibu pimpin.',
      'Saya memiliki pengalaman sebagai Software Developer selama 3 tahun dan yakin dapat berkontribusi positif bagi perusahaan.'
    ],
    closing: 'Demikian surat lamaran ini saya buat dengan sebenar-benarnya. Atas perhatian dan kesempatan yang diberikan, saya ucapkan terima kasih.',
    farewell: 'Hormat saya,',
    signatureName: 'John Doe',
    additionalInfo: 'Software Developer'
  };
  
  const html = compiledTemplate(sampleData);
  console.log('✅ Template rendered successfully');
  console.log(`   HTML length: ${html.length} characters`);
  console.log(`   Contains signature name: ${html.includes(sampleData.signatureName) ? '✅' : '❌'}`);
  console.log(`   Contains company: ${html.includes('PT Example Company') ? '✅' : '❌'}`);
  
  // Save output for inspection
  fs.writeFileSync('./test-simple-output.html', html);
  console.log('   Sample output saved to: test-simple-output.html');
  
} catch (error) {
  console.error('❌ Template compilation/rendering failed:', error.message);
  console.error('   Stack trace:', error.stack);
}

console.log('\n🎯 Core Template Engine Test Complete!');